plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

apply from: "../../library.gradle"
apply from: this.rootProject.file('moduleFlavor.gradle')

android {
    namespace 'com.xiaomi.aivs.capability'

//    composeOptions {
//        kotlinCompilerExtensionVersion "$versions.compose_version"
//    }
//    buildFeatures {
//        viewBinding true
//        compose true
//    }
}

dependencies {
    compileOnly project(':module_basic:library_db')
    compileOnly project(':module_basic:library_miaivs')
    compileOnly project(':module_basic:library_base_common')

    api('com.xiaomi.ai:ai-capability-online-solo:1.0.3') {
        exclude group: "androidx.appcompat", module: "appcompat"
        exclude group: "androidx.fragment", module: "fragment-ktx"
        exclude group: 'com.google.protobuf', module: 'protobuf-javalite'
        exclude group: 'com.xiaomi.account', module: 'oauth-android'
        exclude group: "com.xiaomi.ai", module: "aivs-android-lib"
    }

    api deps.objectbox_kotlin
    api deps.compose_lifecycle_runtime_android
}