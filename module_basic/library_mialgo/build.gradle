import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

apply from: this.rootProject.file('moduleFlavor.gradle')

android {
    namespace 'com.xiaomi.algoprocessor'
    compileSdk rootProject.ext.android.compileSdkVersion


    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        ndk {
            abiFilters.add("arm64-v8a")
        }
    }

    buildTypes {
        debug {
            minifyEnabled false
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility rootProject.ext.android.javaVersion
        targetCompatibility rootProject.ext.android.javaVersion
    }
    tasks.withType(KotlinCompile).configureEach {
        kotlinOptions {
            freeCompilerArgs += ["-Xlint:-deprecation"]
        }
    }
    kotlinOptions {
        jvmTarget = rootProject.ext.android.javaVersion.toString()
    }
    kotlin {
        jvmToolchain(rootProject.ext.android.jvmToolchainVersion)
    }
    lintOptions {
        warningsAsErrors false
        abortOnError false
        disable 'GradleDependency'
    }
    externalNativeBuild {
        cmake {
            path = file("src/main/cpp/CMakeLists.txt")
            version = "3.22.1"
        }
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['jniLibs']
        }
    }
    buildFeatures {
        aidl true
    }
    ndkVersion = "29.0.13113456 rc1"
}

dependencies {
//    api fileTree(dir: 'libs', include: ['*.jar'])
//    api fileTree(dir: 'libs', include: ['*.aar'])
    implementation project(path: ':libs:algo')
    implementation deps.androidx_annotation
    implementation project(path: ':module_basic:library_base')
    implementation project(path: ':module_basic:library_string')
}

detekt {
    config = files("../../ignore-detekt-config.yml")
    buildUponDefaultConfig = true
}