plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

apply from: "../../library.gradle"
apply from: this.rootProject.file('moduleFlavor.gradle')

android {
    namespace 'com.superhexa.music'
}

dependencies {

    api deps.kotlin_stdlib
    api deps.androidx_appcompat
    api deps.androidx_core_core_ktx
    api deps.kotlinx_coroutines_core
    api deps.timber
    api deps.gson
    api deps.mmkv_static
    api project(path: ':libs:music-qmusic')
    api project(path: ':libs:music-neteast')
    api project(path: ':libs:music-xmly')
    implementation project(':module_basic:library_base_common')
    implementation project(':module_basic:library_string')

    api 'androidx.localbroadcastmanager:localbroadcastmanager:1.0.0'

}