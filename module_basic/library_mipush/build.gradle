plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-parcelize' //  支持 kotlin data class Parcelable
}
apply from: this.rootProject.file('moduleFlavor.gradle')

android {
    compileSdk rootProject.ext.android.compileSdkVersion
    

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility rootProject.ext.android.javaVersion
        targetCompatibility rootProject.ext.android.javaVersion
    }
    kotlinOptions {
        jvmTarget = rootProject.ext.android.javaVersion.toString()
    }
    kotlin {
        jvmToolchain(rootProject.ext.android.jvmToolchainVersion)
    }
    lintOptions {
        warningsAsErrors true
        disable 'GradleDependency'
    }
    namespace 'com.superhexa.supervision.library.mipush'
}

dependencies {
    implementation fileTree(dir: 'libs', includes: ['*.jar', '*.aar'])
    compileOnly deps.timber
    compileOnly deps.androidx_appcompat
    implementation deps.kotlinx_coroutines_core
    implementation project(':libs:mipush')
    implementation deps.kotlin_stdlib
    implementation deps.androidx_core_core_ktx
    compileOnly project(':module_basic:library_base_common')
    compileOnly deps.kodein_di_generic_jvm
    implementation project(':module_basic:library_string')
    testImplementation deps.junit
    testImplementation deps.archunit_junit4
    androidTestImplementation deps.androidx_test_junit
    androidTestImplementation deps.androidx_test_espresso_core
}