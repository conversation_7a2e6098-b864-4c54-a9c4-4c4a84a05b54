<?xml version="1.0" encoding="utf-8"?><!DOCTYPE resources [<!ENTITY app_name "小米眼镜">]>
<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="No_Network">网络连接异常，请重试</string>
    <string name="BAD_REQUEST">参数错误</string>
    <string name="NOT_LOGIN">账号登录状态已失效，请重新登录</string>
    <string name="LOGIN_UNKNOWN_ID_TYPE">未知登录类型</string>
    <string name="LOGIN_FAILED">登录失败</string>
    <string name="VERIFICATION_CODE_WRONG">验证码输入有误，请重新输入</string>
    <string name="PWD_WRONG">密码输入有误，请重新输入</string>
    <string name="USER_NOT_EXIST">该账号尚未注册</string>
    <string name="PHONE_OR_EMAIL_EXISTED">账号已存在</string>
    <string name="PARAMS_ILLEGAL">参数不合法</string>
    <string name="PHONE_IS_BLANK">手机号为空</string>
    <string name="PHONE_IS_NOT_VALID">手机号不合法</string>
    <string name="ACCONT_HAS_BIND">该账号已绑定其他用户</string>
    <string name="LOGIN_FAILED_USER_PWD_WRONG">用户名或密码错误</string>
    <string name="GET_PHONE_FAILED_FROM_TELECOM">服务异常，请稍后重试或通过其他方式进行登录</string>
    <string name="SMS_CODE_SENT_TOO_OFTEN">发送验证码过于频繁，请稍后重试</string>
    <string name="PHONE_NUM_CAN_NOT_RECOGNIZED">手机号无法识别</string>
    <string name="VERIFICATION_CODE_EXPIRED">验证码已失效，请重新获取</string>
    <string name="SEND_VERIFICATION_CODE_DAY_LIMIT">验证码发送达到每日上限</string>
    <string name="VERIFICATION_CODE_ERROR">验证码错误</string>
    <string name="VERIFICATION_CODE_TOO_OFTEN">发送验证码过于频繁。</string>
    <string name="USER_NOT_REGISTERED">该账号尚未注册</string>
    <string name="NO_PWD_HAS_BEEN_SET">该账号未设置密码</string>
    <string name="BIND_UNKNOWN_ID_TYPE">需要绑定手机号</string>
    <string name="PLATFORT_UNKNOWN_ID_TYPE">未知平台类型</string>
    <string name="ACCOUNT_UNBIND_NOT_EXIT">解绑三方账号不存在</string>
    <string name="GENDER_UNKNOWN_ID_TYPE">未知性别类型</string>
    <string name="DEVICE_BLE_UNENABLE">未发现绑定设备</string>
    <string name="Unknown_Error">未知错误</string>
    <string name="Request_Error">请求异常，请稍后重试</string>
    <string name="Parse_Error">解析错误</string>
    <string name="Timeout_Error">超时错误</string>
    <string name="UnknownHost">无法访问服务器</string>
    <string name="User_Registered_Other_Region">用户在其它地区注册</string>
    <string name="User_Has_Bind_Other_Type">邮箱已被其他账号绑定</string>

    <string name="upgrade_dialog_title">发现更新</string>
    <string name="upgrade_dialog_description">更新日志：\n\n%s</string>
    <string name="upgrade_dialog_confirm">立即更新</string>
    <string name="upgrade_dialog_confirm_force">强制更新</string>
    <string name="upgrade_dialog_cancel">取消</string>
    <string name="upgrade_is_latest">当前为最新版本</string>

    <string name="deviceUpgrade">升级</string>
    <string name="deviceUpdating">正在升级中…</string>
    <string name="baseVersionError">传入的基础版本和固件版本不匹配</string>
    <string name="otaVersionError">传入的OTA包版本号低于或等于本地存在的OTA包版本号</string>
    <string name="fileSpaceNotEnough">剩余存储空间不足</string>
    <string name="plsUpdateAppAndRetryBind">请将APP升级至最新版本后，再进行绑定</string>
    <string name="bleUnavailable">设备蓝牙断开，请连接后重试</string>
    <string name="bindFailedReason">绑定失败</string>
    <string name="emptyDefault">空空如也</string>
    <string name="cancel">取消</string>
    <string name="sure">确定</string>
    <string name="quit">退出</string>
    <string name="continueSave">继续保存</string>
    <string name="allMeda">所有媒体</string>
    <string name="complete">完成</string>
    <string name="allSelect">全选</string>
    <string name="acquireWriteSDcardFailed">获取读写权限失败</string>
    <string name="acquireCameraFailed">获取相机权限失败</string>
    <string name="acquireAccountFailed">获取账户权限失败</string>
    <string name="denyForeverPlsAccountAllow">被永久拒绝授权，请手动授予账户权限</string>
    <string name="acquireAudioFailed">获取录音权限失败</string>
    <string name="denyForeverPlsAllow">被永久拒绝授权，请手动授予读写权限</string>
    <string name="denyForeverPlsAllowAudio">被永久拒绝授权，请手动授予录音权限</string>
    <string name="denyForeverPlsAllowCamera">被永久拒绝授权，请手动授予相机权限</string>
    <string name="compeltenum">完成（%1$d/%2$d）</string>
    <string name="pickMediacompelte">完成</string>
    <string name="emptyRefresh">刷新</string>
    <string name="emptySetting">设置</string>
    <string name="emptyBack">返回</string>
    <string name="emptyNoPage">页面不见了</string>
    <string name="emptyServiceBasy">服务器忙碌，处理中\n请稍后重试</string>
    <string name="emptyUserIntent">让我看到你想处理的文件</string>
    <string name="NoBindDevice">未绑定设备</string>
    <string name="settingHelp">问题反馈</string>
    <string name="shareto">分享到</string>
    <string name="lackOfPicReSelect">照片或视频被删除，请重新选择</string>
    <string name="iknow">确定</string>
    <string name="password_caption">请输入密码</string>
    <string name="action_send_code">发送验证码</string>
    <string name="send_code_count_down">%d</string>
    <string name="code_retry">重新发送</string>
    <string name="deviceQe">设备电量 %1$s%%</string>

    <string name="configing">设置中</string>
    <string name="configSuccess">设置成功</string>
    <string name="configFailed">设置失败</string>
    <string name="hexaLanguage">zh_CN</string>
    <string name="nextStep">下一步</string>
    <string name="picTitleTimeFormat">yyyy年M月d日</string>

    <string name="provicyTitle">用户协议与隐私政策</string>
    <string name="provicyUpdateTitle">隐私政策更新声明</string>
    <string name="provicyTip">欢迎使用&app_name;\n\n为了给您提供优质的使用体验，&app_name;需联网为您提供相关服务。在使用APP前，请您仔细阅读并同意《&app_name;用户协议》、《&app_name;隐私政策》了解您的用户权益及相关数据的处理方式。为帮助您快速了解我们提供的服务及如何收集您的信息，特向您说明如下：\n\n1. 我们将为您提供绑定智能眼镜、控制智能眼镜等服务，并收集、使用您必要的个人信息；\n\n2. 我们会根据您的使用场景，申请获取您的设备系统权限，系统将通过弹窗征得您的授权，您有权拒绝或撤回这些授权；\n\n3. 我们会采取多方位的安全保护措施以确保您的个人信息安全，除非经过您的同意或相关法律法规规定，我们不会和任何第三方共享您的个人信息。如您同意以上内容，请点击“同意”开始使用我们的产品和服务，感谢您的信任！\n\n</string>
    <string name="provicyAndTerms">请详细阅读《&app_name;用户协议》与《&app_name;隐私政策》后进行同意</string>
    <string name="provicyUpdateTip">&app_name;隐私政策与用户协议已经更新，前往查看。</string>
    <string-array name="home_terms_and_privacy_clicks">
        <item>@string/homeTermsConditions</item>
        <item>@string/homePrivacyConditions</item>
    </string-array>

    <string name="homeTermsConditions">&app_name;用户协议</string>
    <string name="homePrivacyConditions">&app_name;隐私政策</string>
    <string name="sv1PrivacyAndTerms">请详细阅读《眼镜相机用户协议》与《眼镜相机隐私政策》后进行同意</string>
    <string-array name="sv1_home_terms_and_privacy_clicks">
        <item>@string/sv1_homeTermsConditions</item>
        <item>@string/sv1_homePrivacyConditions</item>
    </string-array>
    <string name="sv1_homeTermsConditions">《眼镜相机用户协议》</string>
    <string name="sv1_homePrivacyConditions">《眼镜相机隐私政策》</string>
    <string name="ssprovicyAndTerms">请详细阅读《智能音频眼镜用户协议》与《智能音频眼镜隐私政策》后进行同意</string>
    <string-array name="ss_home_terms_and_privacy_clicks">
        <item>@string/ss_homeTermsConditions</item>
        <item>@string/ss_homePrivacyConditions</item>
    </string-array>

    <string name="ss_homeTermsConditions">《智能音频眼镜用户协议》</string>
    <string name="ss_homePrivacyConditions">《智能音频眼镜隐私政策》</string>
    <string name="provicyUpdateAgree">同意</string>
    <string name="provicyUpdateDisagree">退出</string>
    <string name="provicyNoCheckTip">请勾选同意下方协议</string>

    <string name="notificationTitle">&app_name;</string>
    <string name="spaceNotEnough">剩余空间不足</string>
    <string name="notificationDescDownloading">下载中</string>
    <string name="notificationDescUploading">上传文件中</string>
    <string name="playingWithTraffic">正在使用流量播放</string>
    <string name="aliveHasFinished">直播已结束</string>
    <string name="close">关</string>
    <string name="open">开</string>
    <string name="goOpen">去开启</string>
    <string name="permissionStorageDesc">用于下载设备拍摄的文件、用户反馈读写手机中的照片。如果关闭，将不能实现前述功能。</string>
    <string name="permissionRecordDesc">用于用户反馈拍摄视频时获取声音。如果关闭，将不能实现前述功能。</string>
    <string name="permissionCameraDesc">用于用户反馈时拍摄视频。如果关闭，将不能实现前述功能。</string>
    <string name="permissionAccountDesc">用来获取设备上的账号信息，方便应用程序运行时使用相关帐户进行认证。</string>
    <string name="authorize">去授权</string>
    <string name="has_authorize">已授权</string>
    <string name="hintStoragePermission">请开启读写手机存储权限，\n以便正常使用添加图片或视频功能。</string>
    <string name="libs_feedback_video_limit">视频时长较长，文件较大，请选择30s以内视频</string>
    <string name="music_permission_dialog_title">眼镜APP权限获取</string>
    <string name="music_permission_dialog_describe">在设置中选择“小米眼镜”APP，打开眼镜APP的悬浮窗权限，以支持此应用在后台时语控播放音乐</string>
    <string name="libs_file_space_waiting_trans">%s 个媒体文件待导入</string>
    <string name="libs_import">导入</string>
    <string name="libs_file_space_doing_trans_with_count" tools:ignore="TypographyEllipsis">%s个媒体文件导入中...</string>
</resources>
